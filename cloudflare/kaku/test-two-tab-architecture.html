<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Two-Tab Architecture Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .tab-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .control-tab {
            border-left: 4px solid #007bff;
        }
        .target-tab {
            border-left: 4px solid #28a745;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .test-controls {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Two-Tab Browser Controller Architecture Test</h1>
        <p>This test demonstrates the communication between control tab and target tab in the new two-tab architecture.</p>

        <div class="test-controls">
            <h3>Test Controls</h3>
            <button onclick="initializeControlTab()">1. Initialize Control Tab</button>
            <button onclick="initializeTargetTab()">2. Initialize Target Tab</button>
            <button onclick="testCommunication()">3. Test Communication</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>

        <div class="tab-section control-tab">
            <h2>Control Tab (CDP Operations)</h2>
            <div id="controlStatus" class="status info">Not initialized</div>
            <p>This simulates the control tab that handles all CDP operations and communicates with the target tab.</p>
            <div id="controlLog" class="log"></div>
        </div>

        <div class="tab-section target-tab">
            <h2>Target Tab (Proxy + Scripts)</h2>
            <div id="targetStatus" class="status info">Not initialized</div>
            <p>This simulates the target tab that contains the proxy and other client scripts (screen-cropper, captcha detector, etc.).</p>
            <div id="targetLog" class="log"></div>
        </div>

        <div class="tab-section">
            <h2>Communication Test Results</h2>
            <div id="testResults" class="log"></div>
        </div>
    </div>

    <script>
        let controlTabInitialized = false;
        let targetTabInitialized = false;

        function log(section, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById(section + 'Log');
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(section, message, type = 'info') {
            const statusElement = document.getElementById(section + 'Status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function clearLogs() {
            document.getElementById('controlLog').innerHTML = '';
            document.getElementById('targetLog').innerHTML = '';
            document.getElementById('testResults').innerHTML = '';
        }

        async function initializeControlTab() {
            try {
                log('control', 'Initializing control tab...');
                updateStatus('control', 'Initializing...', 'info');

                // Simulate control tab initialization
                // In real implementation, this would load browser-controller.ts
                log('control', 'Loading browser-controller.ts script...');
                
                // Create a mock control tab environment
                window.__kakuControlTabPort = null;
                window.__kakuControlTabReady = false;
                window.__kakuTargetTabConnected = false;

                // Simulate MessageChannel creation
                const messageChannel = new MessageChannel();
                window.__kakuControlTabPort = messageChannel.port2;
                window.__kakuControlTabReady = true;

                // Mock browserControllerControl global
                window.browserControllerControl = {
                    init: async (wsEndpoint, targetId, targetTabWindow) => {
                        log('control', `Control tab initialized with targetId: ${targetId}`);
                        return Promise.resolve();
                    },
                    setupBrowserMetrics: async (viewport) => {
                        log('control', `Setting up browser metrics: ${JSON.stringify(viewport)}`);
                        return Promise.resolve();
                    },
                    takeScreenshot: async () => {
                        log('control', 'Taking screenshot via CDP...');
                        return Promise.resolve('mock-screenshot-data');
                    }
                };

                controlTabInitialized = true;
                updateStatus('control', 'Control tab initialized successfully', 'success');
                log('control', 'Control tab ready for communication');

            } catch (error) {
                log('control', `Error: ${error.message}`);
                updateStatus('control', 'Initialization failed', 'error');
            }
        }

        async function initializeTargetTab() {
            try {
                log('target', 'Initializing target tab...');
                updateStatus('target', 'Initializing...', 'info');

                if (!controlTabInitialized) {
                    throw new Error('Control tab must be initialized first');
                }

                // Simulate target tab initialization
                // In real implementation, this would load browser-controller-proxy.ts
                log('target', 'Loading browser-controller-proxy.ts script...');

                // Simulate waiting for control tab connection
                log('target', 'Waiting for control tab connection...');
                
                let attempts = 0;
                const maxAttempts = 50;
                
                while (!window.__kakuControlTabReady && attempts < maxAttempts) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }

                if (!window.__kakuControlTabReady) {
                    throw new Error('Control tab connection timeout');
                }

                log('target', 'Found control tab port, establishing connection...');
                
                // Get the port from control tab
                const controlTabPort = window.__kakuControlTabPort;
                
                // Signal connection established
                window.__kakuTargetTabConnected = true;

                // Mock browserController global (proxy)
                window.browserController = {
                    init: async (wsEndpoint, targetId) => {
                        log('target', `Target tab proxy initialized with targetId: ${targetId}`);
                        return Promise.resolve();
                    },
                    setupBrowserMetrics: async (viewport) => {
                        log('target', `Proxy forwarding setupBrowserMetrics: ${JSON.stringify(viewport)}`);
                        // In real implementation, this would send message to control tab
                        return Promise.resolve();
                    },
                    takeScreenshot: async () => {
                        log('target', 'Proxy forwarding takeScreenshot request...');
                        // In real implementation, this would send message to control tab
                        return Promise.resolve('mock-screenshot-data');
                    }
                };

                targetTabInitialized = true;
                updateStatus('target', 'Target tab initialized successfully', 'success');
                log('target', 'Target tab proxy ready');

            } catch (error) {
                log('target', `Error: ${error.message}`);
                updateStatus('target', 'Initialization failed', 'error');
            }
        }

        async function testCommunication() {
            const testLog = document.getElementById('testResults');
            testLog.innerHTML = '';
            
            function testResult(message) {
                const timestamp = new Date().toLocaleTimeString();
                testLog.innerHTML += `[${timestamp}] ${message}\n`;
                testLog.scrollTop = testLog.scrollHeight;
            }

            try {
                testResult('Starting communication tests...');

                if (!controlTabInitialized || !targetTabInitialized) {
                    throw new Error('Both tabs must be initialized before testing');
                }

                // Test 1: Basic API availability
                testResult('✓ Test 1: Checking API availability');
                testResult(`  - Control tab browserControllerControl: ${typeof window.browserControllerControl}`);
                testResult(`  - Target tab browserController: ${typeof window.browserController}`);

                // Test 2: Method calls
                testResult('✓ Test 2: Testing method calls');
                
                await window.browserController.setupBrowserMetrics({ width: 1024, height: 768 });
                testResult('  - setupBrowserMetrics call successful');

                const screenshot = await window.browserController.takeScreenshot();
                testResult(`  - takeScreenshot call successful: ${screenshot ? 'data received' : 'no data'}`);

                // Test 3: Global state verification
                testResult('✓ Test 3: Verifying global state');
                testResult(`  - Control tab ready: ${window.__kakuControlTabReady}`);
                testResult(`  - Target tab connected: ${window.__kakuTargetTabConnected}`);
                testResult(`  - MessageChannel port available: ${window.__kakuControlTabPort ? 'yes' : 'no'}`);

                testResult('✅ All tests passed! Two-tab architecture is working correctly.');

            } catch (error) {
                testResult(`❌ Test failed: ${error.message}`);
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            log('control', 'Page loaded, ready for testing');
            log('target', 'Page loaded, ready for testing');
        });
    </script>
</body>
</html>
