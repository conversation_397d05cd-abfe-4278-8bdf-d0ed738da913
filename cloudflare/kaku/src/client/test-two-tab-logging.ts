/**
 * Test script to verify two-tab architecture logging
 * 
 * This script can be injected into the target tab to test that events
 * are being properly forwarded to the control tab and logged.
 */

(function testTwoTabLogging() {
  console.log('🧪 TEST SCRIPT: Two-tab logging test initialized');

  // Wait for browser controller to be available
  function waitForBrowserController(): Promise<void> {
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (window.browserController) {
          clearInterval(checkInterval);
          console.log('✅ TEST SCRIPT: Browser controller found');
          resolve();
        }
      }, 100);
    });
  }

  // Test mouse events
  async function testMouseEvents() {
    console.log('🖱️ TEST SCRIPT: Testing mouse events...');
    
    try {
      // Test mouse move
      await window.browserController.dispatchMouseMove(100, 100);
      console.log('✅ TEST SCRIPT: Mouse move test completed');
      
      // Test mouse click
      await window.browserController.dispatchMouseClick(200, 200);
      console.log('✅ TEST SCRIPT: Mouse click test completed');
      
    } catch (error) {
      console.error('❌ TEST SCRIPT: Mouse event test failed:', error);
    }
  }

  // Test keyboard events
  async function testKeyboardEvents() {
    console.log('⌨️ TEST SCRIPT: Testing keyboard events...');
    
    try {
      // Test key press
      await window.browserController.dispatchKeyEvent({
        type: 'keyDown',
        key: 'a',
        code: 'KeyA',
        windowsVirtualKeyCode: 65
      });
      
      await window.browserController.dispatchKeyEvent({
        type: 'keyUp',
        key: 'a',
        code: 'KeyA',
        windowsVirtualKeyCode: 65
      });
      
      console.log('✅ TEST SCRIPT: Key event test completed');
      
      // Test text insertion
      await window.browserController.insertText('Hello Two-Tab Architecture!');
      console.log('✅ TEST SCRIPT: Text insertion test completed');
      
    } catch (error) {
      console.error('❌ TEST SCRIPT: Keyboard event test failed:', error);
    }
  }

  // Test browser metrics
  async function testBrowserMetrics() {
    console.log('📐 TEST SCRIPT: Testing browser metrics...');
    
    try {
      await window.browserController.setupBrowserMetrics({ width: 1024, height: 768 });
      console.log('✅ TEST SCRIPT: Browser metrics test completed');
    } catch (error) {
      console.error('❌ TEST SCRIPT: Browser metrics test failed:', error);
    }
  }

  // Test screenshot
  async function testScreenshot() {
    console.log('📸 TEST SCRIPT: Testing screenshot...');
    
    try {
      const screenshot = await window.browserController.takeScreenshot();
      console.log('✅ TEST SCRIPT: Screenshot test completed, size:', screenshot.length);
    } catch (error) {
      console.error('❌ TEST SCRIPT: Screenshot test failed:', error);
    }
  }

  // Run all tests
  async function runAllTests() {
    console.log('🚀 TEST SCRIPT: Starting comprehensive two-tab test...');
    
    await waitForBrowserController();
    
    // Run tests with delays to see logging clearly
    await testBrowserMetrics();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testMouseEvents();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testKeyboardEvents();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testScreenshot();
    
    console.log('🎉 TEST SCRIPT: All tests completed! Check the control tab console for CDP execution logs.');
  }

  // Add event listeners to test real user interactions
  function setupRealEventListeners() {
    console.log('👂 TEST SCRIPT: Setting up real event listeners...');
    
    // Listen for real mouse moves
    document.addEventListener('mousemove', (event) => {
      console.log(`🖱️ TEST SCRIPT: Real mouse move detected: (${event.clientX}, ${event.clientY})`);
      // Forward to browser controller
      if (window.browserController) {
        window.browserController.dispatchMouseMove(event.clientX, event.clientY);
      }
    });
    
    // Listen for real clicks
    document.addEventListener('click', (event) => {
      console.log(`🖱️ TEST SCRIPT: Real click detected: (${event.clientX}, ${event.clientY})`);
      // Forward to browser controller
      if (window.browserController) {
        window.browserController.dispatchMouseClick(event.clientX, event.clientY);
      }
    });
    
    // Listen for real key presses
    document.addEventListener('keydown', (event) => {
      console.log(`⌨️ TEST SCRIPT: Real keydown detected: ${event.key}`);
      // Forward to browser controller
      if (window.browserController) {
        window.browserController.dispatchKeyEvent({
          type: 'keyDown',
          key: event.key,
          code: event.code,
          windowsVirtualKeyCode: event.keyCode
        });
      }
    });
    
    console.log('✅ TEST SCRIPT: Real event listeners setup complete');
  }

  // Expose test functions globally for manual testing
  window.twoTabTest = {
    runAllTests,
    testMouseEvents,
    testKeyboardEvents,
    testBrowserMetrics,
    testScreenshot,
    setupRealEventListeners
  };

  // Auto-run tests after a short delay
  setTimeout(() => {
    runAllTests();
    setupRealEventListeners();
  }, 2000);

  console.log('🧪 TEST SCRIPT: Two-tab logging test ready. Use window.twoTabTest.* for manual testing.');
})();

// Type declarations for TypeScript
declare global {
  interface Window {
    browserController: {
      dispatchMouseMove: (x: number, y: number) => Promise<void>;
      dispatchMouseClick: (x: number, y: number) => Promise<void>;
      dispatchKeyEvent: (params: any) => Promise<void>;
      insertText: (text: string) => Promise<void>;
      setupBrowserMetrics: (viewport: { width: number; height: number }) => Promise<void>;
      takeScreenshot: () => Promise<string>;
    };
    twoTabTest: {
      runAllTests: () => Promise<void>;
      testMouseEvents: () => Promise<void>;
      testKeyboardEvents: () => Promise<void>;
      testBrowserMetrics: () => Promise<void>;
      testScreenshot: () => Promise<void>;
      setupRealEventListeners: () => void;
    };
  }
}
